<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services - Pracinov</title>
    <meta name="description" content="Explore Pracinov's comprehensive IT services including software development, cloud solutions, and digital transformation.">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/assets/css/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
</head>
<body>
    <!-- Header -->
    <header class="header-hero-gradient fixed w-full top-0 z-50">
        <div class="container">
            <div class="flex items-center justify-between h-20">
                <div class="flex items-center">
                    <span class="material-icons text-white text-4xl">integration_instructions</span>
                    <span class="ml-3 text-3xl font-bold text-white">Pracinov</span>
                </div>
                <nav class="hidden md:flex space-x-6 items-center">
                    <a class="nav-link" href="/pages/home/">Home</a>
                    <a class="nav-link" href="/pages/about/">About Us</a>
                    <a class="nav-link active" href="/pages/services/">Services</a>
                    <a class="nav-link" href="/pages/life/">Life@ Pracinov</a>
                    <a class="nav-link" href="/pages/careers/">Careers</a>
                </nav>
                <button class="hidden md:block btn btn-secondary">
                    Book Consultation
                </button>
                <div class="md:hidden">
                    <button class="text-white hover:text-white focus:outline-none" id="mobile-menu-button">
                        <span class="material-icons text-3xl">menu</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="md:hidden hidden bg-white/10 backdrop-blur-sm" id="mobile-menu">
            <div class="container space-y-1 py-4">
                <a class="block nav-link text-center text-lg py-3" href="/pages/home/">Home</a>
                <a class="block nav-link text-center text-lg py-3" href="/pages/about/">About Us</a>
                <a class="block nav-link active text-center text-lg py-3" href="/pages/services/">Services</a>
                <a class="block nav-link text-center text-lg py-3" href="/pages/life/">Life@ Pracinov</a>
                <a class="block nav-link text-center text-lg py-3" href="/pages/careers/">Careers</a>
                <div class="pt-4">
                    <button class="w-full btn btn-secondary">
                        Book Consultation
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="hero-section-new">
            <div class="container relative z-10">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[500px]">
                    <!-- Left Content -->
                    <div class="text-left">
                        <h1 class="text-3xl lg:text-5xl font-bold text-white mb-6 leading-tight">
                            Empowering Your Business with Cutting-Edge IT Solutions
                        </h1>
                        <p class="text-base lg:text-lg text-white/90 mb-8 leading-relaxed">
                            Transform your business with our comprehensive IT services, designed to drive innovation and growth in today's digital landscape.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button class="bg-white text-purple-700 font-medium py-4 px-8 rounded-lg text-base shadow-lg hover:shadow-xl transition-all duration-200 ease-in-out hover:bg-gray-50">
                                Get Started
                            </button>
                            <button class="border-2 border-white text-white font-medium py-4 px-8 rounded-lg text-base hover:bg-white hover:text-purple-700 transition-all duration-200 ease-in-out">
                                Learn More
                            </button>
                        </div>
                    </div>

                    <!-- Right Image -->
                    <div class="relative lg:block hidden">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2015&q=80"
                                 alt="IT Solutions and Technology"
                                 class="rounded-2xl shadow-2xl opacity-90 w-full h-[400px] object-cover">
                            <div class="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-transparent rounded-2xl"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Offerings -->
        <section class="bg-white" style="padding: 4rem 0;">
            <div class="container">
                <div class="text-center mb-12">
                    <h2 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">Engineering Excellence Across Every Domain</h2>
                    <p class="text-base text-gray-600 max-w-3xl mx-auto">As CTO, I've architected these service offerings to solve real engineering challenges. Each solution is battle-tested in production environments and designed for scale.</p>
                </div>

                <!-- Primary Services Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
                    <!-- Elite Engineering Teams -->
                    <div class="bg-gradient-to-br from-indigo-50 to-blue-50 rounded-xl p-8 border border-indigo-100 hover:shadow-lg transition-all duration-300">
                        <div class="flex items-center justify-center w-16 h-16 rounded-full bg-indigo-600 text-white mb-6 mx-auto">
                            <span class="material-icons text-2xl">engineering</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-4 text-center">Elite Engineering Teams</h3>
                        <p class="text-gray-700 leading-relaxed mb-6 text-sm">Hand-picked senior engineers who've shipped products at scale. No junior developers masquerading as seniors—every team member has 5+ years solving complex distributed systems challenges.</p>

                        <div class="space-y-4">
                            <div class="bg-white rounded-lg p-4 border border-indigo-100">
                                <h4 class="font-semibold text-gray-900 mb-2 text-sm">🚀 Rapid Team Assembly</h4>
                                <p class="text-gray-600 text-xs">Full-stack teams deployed in 72 hours. Pre-vetted architects, senior developers, and DevOps engineers ready to integrate with your existing workflows.</p>
                            </div>
                            <div class="bg-white rounded-lg p-4 border border-indigo-100">
                                <h4 class="font-semibold text-gray-900 mb-2 text-sm">⚡ Performance-First Mindset</h4>
                                <p class="text-gray-600 text-xs">Engineers who think in microseconds, not just features. Every line of code is optimized for performance, scalability, and maintainability.</p>
                            </div>
                            <div class="bg-white rounded-lg p-4 border border-indigo-100">
                                <h4 class="font-semibold text-gray-900 mb-2 text-sm">🔧 Technology Agnostic</h4>
                                <p class="text-gray-600 text-xs">React, Vue, Angular frontends. Node.js, Python, Go, Rust backends. AWS, GCP, Azure cloud. We adapt to your stack, not force ours.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Architecture & Development -->
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-8 border border-purple-100 hover:shadow-lg transition-all duration-300">
                        <div class="flex items-center justify-center w-16 h-16 rounded-full bg-purple-600 text-white mb-6 mx-auto">
                            <span class="material-icons text-2xl">architecture</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-4 text-center">System Architecture & Development</h3>
                        <p class="text-gray-700 leading-relaxed mb-6 text-sm">We don't just code—we architect systems that survive Black Friday traffic spikes. Every solution is designed with failure modes, monitoring, and horizontal scaling in mind.</p>

                        <div class="space-y-4">
                            <div class="bg-white rounded-lg p-4 border border-purple-100">
                                <h4 class="font-semibold text-gray-900 mb-2 text-sm">🏗️ Microservices Architecture</h4>
                                <p class="text-gray-600 text-xs">Event-driven architectures with proper service boundaries. API gateways, message queues, and distributed tracing built-in from day one.</p>
                            </div>
                            <div class="bg-white rounded-lg p-4 border border-purple-100">
                                <h4 class="font-semibold text-gray-900 mb-2 text-sm">📱 Modern Frontend Engineering</h4>
                                <p class="text-gray-600 text-xs">Component-driven development with design systems. Progressive Web Apps, server-side rendering, and edge computing optimizations.</p>
                            </div>
                            <div class="bg-white rounded-lg p-4 border border-purple-100">
                                <h4 class="font-semibold text-gray-900 mb-2 text-sm">🔄 CI/CD Pipeline Mastery</h4>
                                <p class="text-gray-600 text-xs">Zero-downtime deployments with automated testing, security scanning, and rollback mechanisms. Infrastructure as Code with Terraform and Kubernetes.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Platform Engineering -->
                    <div class="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-xl p-8 border border-emerald-100 hover:shadow-lg transition-all duration-300">
                        <div class="flex items-center justify-center w-16 h-16 rounded-full bg-emerald-600 text-white mb-6 mx-auto">
                            <span class="material-icons text-2xl">cloud_sync</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-4 text-center">Platform Engineering & SRE</h3>
                        <p class="text-gray-700 leading-relaxed mb-6 text-sm">Beyond traditional DevOps—we build internal developer platforms that make your engineering teams 10x more productive. Observability, automation, and reliability engineering at its core.</p>

                        <div class="space-y-4">
                            <div class="bg-white rounded-lg p-4 border border-emerald-100">
                                <h4 class="font-semibold text-gray-900 mb-2 text-sm">📊 Observability Stack</h4>
                                <p class="text-gray-600 text-xs">Prometheus, Grafana, Jaeger, and ELK stack. Custom dashboards that show business metrics, not just server metrics. Alert fatigue is eliminated.</p>
                            </div>
                            <div class="bg-white rounded-lg p-4 border border-emerald-100">
                                <h4 class="font-semibold text-gray-900 mb-2 text-sm">🛡️ Security-First Operations</h4>
                                <p class="text-gray-600 text-xs">Zero-trust networking, secrets management, vulnerability scanning, and compliance automation. Security built into every layer, not bolted on.</p>
                            </div>
                            <div class="bg-white rounded-lg p-4 border border-emerald-100">
                                <h4 class="font-semibold text-gray-900 mb-2 text-sm">⚙️ Self-Healing Systems</h4>
                                <p class="text-gray-600 text-xs">Automated incident response, chaos engineering, and predictive scaling. Systems that recover from failures faster than humans can detect them.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Specialized Services -->
                <div class="bg-gray-50 rounded-xl p-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-6 text-center">Specialized Engineering Capabilities</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="material-icons text-blue-600">memory</span>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2 text-sm">AI/ML Engineering</h4>
                            <p class="text-gray-600 text-xs">MLOps pipelines, model serving, and real-time inference systems</p>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="material-icons text-green-600">account_tree</span>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2 text-sm">Blockchain Development</h4>
                            <p class="text-gray-600 text-xs">Smart contracts, DeFi protocols, and Web3 integrations</p>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="material-icons text-purple-600">speed</span>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2 text-sm">Performance Engineering</h4>
                            <p class="text-gray-600 text-xs">Database optimization, caching strategies, and load testing</p>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="material-icons text-orange-600">transform</span>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2 text-sm">Legacy Modernization</h4>
                            <p class="text-gray-600 text-xs">Strangler fig patterns, API-first migrations, and zero-downtime transitions</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Technology Stack Section -->
        <section class="bg-white py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Our Technology Arsenal</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">We leverage cutting-edge technologies across the full stack to deliver robust, scalable solutions</p>
                </div>

                <!-- Technology Categories -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span class="material-icons text-blue-600 text-2xl">web</span>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Frontend</h3>
                        <p class="text-sm text-gray-600">Modern UI frameworks</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span class="material-icons text-green-600 text-2xl">dns</span>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Backend</h3>
                        <p class="text-sm text-gray-600">Scalable server solutions</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span class="material-icons text-purple-600 text-2xl">cloud</span>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Cloud</h3>
                        <p class="text-sm text-gray-600">Enterprise cloud platforms</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span class="material-icons text-orange-600 text-2xl">psychology</span>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">AI/ML</h3>
                        <p class="text-sm text-gray-600">Intelligent solutions</p>
                    </div>
                </div>

                <!-- Dynamic Technology Carousel -->
                <div class="relative overflow-hidden bg-gray-50 rounded-2xl py-12">
                    <div class="absolute inset-0 bg-gradient-to-r from-gray-50 via-transparent to-gray-50 z-10 pointer-events-none"></div>

                    <!-- Scrolling Container -->
                    <div class="tech-carousel flex items-center space-x-8 animate-scroll">
                        <!-- Frontend Technologies -->
                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg" alt="React" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">React</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">UI Library</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/vuejs/vuejs-original.svg" alt="Vue.js" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Vue.js</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Progressive Framework</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/angularjs/angularjs-original.svg" alt="Angular" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Angular</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Full Framework</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg" alt="Next.js" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Next.js</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">React Framework</p>
                        </div>

                        <!-- Backend Technologies -->
                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg" alt="Node.js" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Node.js</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">JavaScript Runtime</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/express/express-original.svg" alt="Express.js" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Express.js</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Web Framework</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/java/java-original.svg" alt="Java" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Java</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Enterprise Language</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg" alt="Spring Boot" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Spring Boot</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Java Framework</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg" alt="Python" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Python</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Versatile Language</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/django/django-plain.svg" alt="Django" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Django</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Python Framework</p>
                        </div>

                        <!-- Databases -->
                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg" alt="MongoDB" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">MongoDB</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">NoSQL Database</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg" alt="MySQL" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">MySQL</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Relational DB</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg" alt="PostgreSQL" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">PostgreSQL</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Advanced SQL</p>
                        </div>

                        <!-- Cloud Platforms -->
                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg" alt="AWS" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">AWS</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Cloud Platform</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/azure/azure-original.svg" alt="Azure" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Azure</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Microsoft Cloud</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg" alt="Docker" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Docker</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Containerization</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/kubernetes/kubernetes-plain.svg" alt="Kubernetes" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Kubernetes</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Orchestration</p>
                        </div>

                        <!-- AI/ML Tools -->
                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/tensorflow/tensorflow-original.svg" alt="TensorFlow" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">TensorFlow</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">ML Framework</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/pytorch/pytorch-original.svg" alt="PyTorch" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">PyTorch</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Deep Learning</p>
                        </div>

                        <!-- Duplicate items for seamless loop -->
                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg" alt="React" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">React</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">UI Library</p>
                        </div>

                        <div class="tech-item bg-white rounded-xl p-6 shadow-lg border border-gray-100 min-w-[200px]">
                            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/vuejs/vuejs-original.svg" alt="Vue.js" class="w-12 h-12 mx-auto mb-3">
                            <h4 class="font-semibold text-gray-900 text-center">Vue.js</h4>
                            <p class="text-xs text-gray-600 text-center mt-1">Progressive Framework</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- The Pracinov Engineering Philosophy -->
        <section class="bg-gray-50 py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">The Pracinov Engineering Philosophy</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">After 15 years building systems that power millions of users, I've learned what separates good engineering from exceptional engineering. Here's what makes us different.</p>
                </div>

                <!-- Core Philosophy Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
                    <!-- Code Quality Obsession -->
                    <div class="group bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-purple-100 hover:border-purple-200 transform hover:-translate-y-2 cursor-pointer relative overflow-hidden">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 opacity-5">
                            <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                                <pattern id="code-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                                    <circle cx="2" cy="2" r="1" fill="currentColor"/>
                                    <circle cx="12" cy="12" r="1" fill="currentColor"/>
                                </pattern>
                                <rect width="100" height="100" fill="url(#code-pattern)"/>
                            </svg>
                        </div>

                        <div class="relative z-10">
                            <div class="flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-purple-500 to-indigo-600 text-white mb-6 mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-4 text-center group-hover:text-purple-700 transition-colors">Code Quality Obsession</h3>
                            <p class="text-gray-600 text-sm mb-6 text-center leading-relaxed">We treat code like poetry—every line matters, every function has purpose, every architecture decision is deliberate.</p>

                            <div class="space-y-3">
                                <div class="flex items-center text-gray-700 text-sm bg-white/60 rounded-lg p-3 group-hover:bg-white/80 transition-colors">
                                    <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                        <span class="material-icons text-purple-600 text-sm">verified</span>
                                    </div>
                                    <span>100% test coverage on critical paths</span>
                                </div>
                                <div class="flex items-center text-gray-700 text-sm bg-white/60 rounded-lg p-3 group-hover:bg-white/80 transition-colors">
                                    <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                        <span class="material-icons text-purple-600 text-sm">verified</span>
                                    </div>
                                    <span>Automated code reviews with SonarQube</span>
                                </div>
                                <div class="flex items-center text-gray-700 text-sm bg-white/60 rounded-lg p-3 group-hover:bg-white/80 transition-colors">
                                    <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                        <span class="material-icons text-purple-600 text-sm">verified</span>
                                    </div>
                                    <span>Performance budgets enforced in CI/CD</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Operational Excellence -->
                    <div class="group bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-blue-100 hover:border-blue-200 transform hover:-translate-y-2 cursor-pointer relative overflow-hidden">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 opacity-5">
                            <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                                <pattern id="ops-pattern" x="0" y="0" width="15" height="15" patternUnits="userSpaceOnUse">
                                    <rect x="2" y="2" width="2" height="2" fill="currentColor"/>
                                    <rect x="8" y="8" width="2" height="2" fill="currentColor"/>
                                </pattern>
                                <rect width="100" height="100" fill="url(#ops-pattern)"/>
                            </svg>
                        </div>

                        <div class="relative z-10">
                            <div class="flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-600 text-white mb-6 mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-4 text-center group-hover:text-blue-700 transition-colors">Operational Excellence</h3>
                            <p class="text-gray-600 text-sm mb-6 text-center leading-relaxed">We don't just ship features—we ship systems that run themselves. Monitoring, alerting, and automation are first-class citizens.</p>

                            <div class="space-y-3">
                                <div class="flex items-center text-gray-700 text-sm bg-white/60 rounded-lg p-3 group-hover:bg-white/80 transition-colors">
                                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                        <span class="material-icons text-blue-600 text-sm">insights</span>
                                    </div>
                                    <span>Real-time business metrics dashboards</span>
                                </div>
                                <div class="flex items-center text-gray-700 text-sm bg-white/60 rounded-lg p-3 group-hover:bg-white/80 transition-colors">
                                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                        <span class="material-icons text-blue-600 text-sm">insights</span>
                                    </div>
                                    <span>Predictive scaling and cost optimization</span>
                                </div>
                                <div class="flex items-center text-gray-700 text-sm bg-white/60 rounded-lg p-3 group-hover:bg-white/80 transition-colors">
                                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                        <span class="material-icons text-blue-600 text-sm">insights</span>
                                    </div>
                                    <span>Incident response in under 2 minutes</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Innovation Mindset -->
                    <div class="group bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-emerald-100 hover:border-emerald-200 transform hover:-translate-y-2 cursor-pointer relative overflow-hidden">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 opacity-5">
                            <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                                <pattern id="innovation-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse">
                                    <circle cx="5" cy="5" r="2" fill="currentColor"/>
                                    <circle cx="15" cy="15" r="1" fill="currentColor"/>
                                </pattern>
                                <rect width="100" height="100" fill="url(#innovation-pattern)"/>
                            </svg>
                        </div>

                        <div class="relative z-10">
                            <div class="flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-emerald-500 to-teal-600 text-white mb-6 mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-4 text-center group-hover:text-emerald-700 transition-colors">Innovation Mindset</h3>
                            <p class="text-gray-600 text-sm mb-6 text-center leading-relaxed">We don't follow trends—we set them. Every project is an opportunity to push boundaries and explore new possibilities.</p>

                            <div class="space-y-3">
                                <div class="flex items-center text-gray-700 text-sm bg-white/60 rounded-lg p-3 group-hover:bg-white/80 transition-colors">
                                    <div class="w-6 h-6 bg-emerald-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                        <span class="material-icons text-emerald-600 text-sm">auto_awesome</span>
                                    </div>
                                    <span>Bleeding-edge tech evaluation</span>
                                </div>
                                <div class="flex items-center text-gray-700 text-sm bg-white/60 rounded-lg p-3 group-hover:bg-white/80 transition-colors">
                                    <div class="w-6 h-6 bg-emerald-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                        <span class="material-icons text-emerald-600 text-sm">auto_awesome</span>
                                    </div>
                                    <span>Open-source contributions</span>
                                </div>
                                <div class="flex items-center text-gray-700 text-sm bg-white/60 rounded-lg p-3 group-hover:bg-white/80 transition-colors">
                                    <div class="w-6 h-6 bg-emerald-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                        <span class="material-icons text-emerald-600 text-sm">auto_awesome</span>
                                    </div>
                                    <span>Internal R&D time allocation</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- The Pracinov Difference -->
                <div class="relative bg-gradient-to-br from-purple-50 via-indigo-50 to-blue-50 rounded-2xl p-8 shadow-lg border border-purple-100 overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 opacity-5">
                        <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                            <pattern id="difference-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse">
                                <circle cx="5" cy="5" r="1.5" fill="currentColor"/>
                                <circle cx="15" cy="15" r="1" fill="currentColor"/>
                                <circle cx="25" cy="10" r="0.5" fill="currentColor"/>
                            </pattern>
                            <rect width="100" height="100" fill="url(#difference-pattern)"/>
                        </svg>
                    </div>

                    <!-- Floating Elements -->
                    <div class="absolute top-4 right-4 w-16 h-16 bg-gradient-to-br from-purple-200 to-indigo-200 rounded-full opacity-20 animate-pulse"></div>
                    <div class="absolute bottom-4 left-4 w-12 h-12 bg-gradient-to-br from-blue-200 to-cyan-200 rounded-full opacity-20 animate-pulse" style="animation-delay: 1s;"></div>

                    <div class="relative z-10">
                        <div class="text-center mb-8">
                            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl mb-4 shadow-lg">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-4">The Pracinov Difference</h3>
                            <p class="text-gray-600 max-w-2xl mx-auto">What happens when you combine Silicon Valley engineering culture with enterprise-grade reliability</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            <div class="text-center bg-white/60 rounded-xl p-6 backdrop-blur-sm border border-white/50 hover:bg-white/80 transition-all duration-300 hover:transform hover:-translate-y-1">
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="text-3xl font-bold text-purple-600 mb-2">99.99%</div>
                                <div class="text-gray-900 font-medium">System Uptime</div>
                                <div class="text-gray-500 text-sm mt-1">SLA-backed guarantee</div>
                            </div>
                            <div class="text-center bg-white/60 rounded-xl p-6 backdrop-blur-sm border border-white/50 hover:bg-white/80 transition-all duration-300 hover:transform hover:-translate-y-1">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                <div class="text-3xl font-bold text-blue-600 mb-2">&lt;100ms</div>
                                <div class="text-gray-900 font-medium">API Response Time</div>
                                <div class="text-gray-500 text-sm mt-1">P95 latency target</div>
                            </div>
                            <div class="text-center bg-white/60 rounded-xl p-6 backdrop-blur-sm border border-white/50 hover:bg-white/80 transition-all duration-300 hover:transform hover:-translate-y-1">
                                <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="text-3xl font-bold text-emerald-600 mb-2">72hrs</div>
                                <div class="text-gray-900 font-medium">Team Deployment</div>
                                <div class="text-gray-500 text-sm mt-1">From contract to code</div>
                            </div>
                            <div class="text-center bg-white/60 rounded-xl p-6 backdrop-blur-sm border border-white/50 hover:bg-white/80 transition-all duration-300 hover:transform hover:-translate-y-1">
                                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                    </svg>
                                </div>
                                <div class="text-3xl font-bold text-indigo-600 mb-2">24/7</div>
                                <div class="text-gray-900 font-medium">Engineering Support</div>
                                <div class="text-gray-500 text-sm mt-1">Follow-the-sun coverage</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="bg-white" style="padding: 3rem 0;">
            <div class="container">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div>
                        <h2 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">Ready to Elevate Your Business?</h2>
                        <p class="text-sm lg:text-base text-gray-600 mb-6 leading-relaxed">Request a free consultation to discover how our IT services can drive your success and transform your business operations.</p>

                        <div class="space-y-3 mb-6">
                            <div class="flex items-center">
                                <div class="flex items-center justify-center w-6 h-6 rounded-full bg-indigo-600 text-white mr-3 flex-shrink-0">
                                    <span class="material-icons text-sm">check</span>
                                </div>
                                <span class="text-gray-700 font-medium text-sm">Free Initial Consultation</span>
                            </div>
                            <div class="flex items-center">
                                <div class="flex items-center justify-center w-6 h-6 rounded-full bg-indigo-600 text-white mr-3 flex-shrink-0">
                                    <span class="material-icons text-sm">check</span>
                                </div>
                                <span class="text-gray-700 font-medium text-sm">Custom Solution Design</span>
                            </div>
                            <div class="flex items-center">
                                <div class="flex items-center justify-center w-6 h-6 rounded-full bg-indigo-600 text-white mr-3 flex-shrink-0">
                                    <span class="material-icons text-sm">check</span>
                                </div>
                                <span class="text-gray-700 font-medium text-sm">24/7 Ongoing Support</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-8">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Request a Free Consultation</h3>
                        <form class="space-y-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                                <input type="text" id="name" name="name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm" placeholder="Your full name">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                <input type="email" id="email" name="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm" placeholder="<EMAIL>">
                            </div>
                            <div>
                                <label for="company" class="block text-sm font-medium text-gray-700 mb-2">Company</label>
                                <input type="text" id="company" name="company" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm" placeholder="Your company name">
                            </div>
                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Project Details</label>
                                <textarea id="message" name="message" rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm" placeholder="Tell us about your project requirements..."></textarea>
                            </div>
                            <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-6 rounded-lg text-sm shadow-lg hover:shadow-xl transition-all duration-200 ease-in-out">
                                Send Request
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer-bg section">
        <div class="container relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                <div>
                    <div class="flex items-center mb-6">
                        <span class="material-icons text-white text-3xl mr-3">integration_instructions</span>
                        <h3 class="text-2xl font-bold text-white">Pracinov</h3>
                    </div>
                    <p class="text-white/80 leading-relaxed mb-6">Premium IT solutions for forward-thinking businesses.</p>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-white mb-6">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="/pages/about/" class="text-white/80 hover:text-white transition-colors duration-300">About Us</a></li>
                        <li><a href="/pages/services/" class="text-white/80 hover:text-white transition-colors duration-300">Our Services</a></li>
                        <li><a href="/pages/portfolio/" class="text-white/80 hover:text-white transition-colors duration-300">Portfolio</a></li>
                        <li><a href="/pages/careers/" class="text-white/80 hover:text-white transition-colors duration-300">Careers</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-white mb-6">Our Services</h3>
                    <ul class="space-y-3">
                        <li><a href="/pages/services/" class="text-white/80 hover:text-white transition-colors duration-300">IT Staffing Solutions</a></li>
                        <li><a href="/pages/services/" class="text-white/80 hover:text-white transition-colors duration-300">Custom Development</a></li>
                        <li><a href="/pages/services/" class="text-white/80 hover:text-white transition-colors duration-300">Maintenance & Support</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-white mb-6">Get in Touch</h3>
                    <ul class="space-y-3">
                        <li class="flex items-center">
                            <span class="material-icons text-white/60 mr-3">email</span>
                            <a href="mailto:<EMAIL>" class="text-white/80 hover:text-white transition-colors duration-300"><EMAIL></a>
                        </li>
                        <li class="flex items-center">
                            <span class="material-icons text-white/60 mr-3">phone</span>
                            <a href="tel:+1234567890" class="text-white/80 hover:text-white transition-colors duration-300">+1 (234) 567-890</a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-white/20 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-white/60 text-sm">&copy; 2024 Pracinov. All rights reserved.</p>
                    <div class="flex space-x-6 mt-4 md:mt-0">
                        <a href="#" class="text-white/60 hover:text-white text-sm transition-colors duration-300">Privacy Policy</a>
                        <a href="#" class="text-white/60 hover:text-white text-sm transition-colors duration-300">Terms of Service</a>
                        <a href="#" class="text-white/60 hover:text-white text-sm transition-colors duration-300">Cookie Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button id="scroll-to-top" onclick="scrollToTop()">
        <span class="material-icons">keyboard_arrow_up</span>
    </button>

    <!-- Scripts -->
    <script src="/assets/js/main.js"></script>
</body>
</html>
