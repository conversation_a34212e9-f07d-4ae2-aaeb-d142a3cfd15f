/* Pracinov Website - Modern Design System */

/* ===== STEP 1: CSS RESET & FOUNDATION ===== */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #FAFBFC;
    line-height: 1.6;
    color: #1F2937;
    overflow-x: hidden;
}

/* ===== STEP 2: SPACING SYSTEM ===== */
:root {
    /* Base spacing units */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */
    --space-4xl: 6rem;      /* 96px */
    --space-5xl: 8rem;      /* 128px */

    /* Container boundaries */
    --container-xs: 480px;
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1400px;
    --container-max: 1600px;

    /* Content width limits */
    --content-narrow: 65ch;
    --content-wide: 85ch;

    /* Border radius system */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Shadow system */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Color system */
    --color-primary: #4F46E5;
    --color-primary-dark: #3730A3;
    --color-secondary: #7C3AED;
    --color-accent: #06B6D4;
    --color-success: #10B981;
    --color-warning: #F59E0B;
    --color-error: #EF4444;

    /* Neutral colors */
    --color-white: #FFFFFF;
    --color-gray-50: #F9FAFB;
    --color-gray-100: #F3F4F6;
    --color-gray-200: #E5E7EB;
    --color-gray-300: #D1D5DB;
    --color-gray-400: #9CA3AF;
    --color-gray-500: #6B7280;
    --color-gray-600: #4B5563;
    --color-gray-700: #374151;
    --color-gray-800: #1F2937;
    --color-gray-900: #111827;
}

/* ===== STEP 1 & 4: CONTAINER BOUNDARIES ===== */
.container {
    width: 100%;
    max-width: var(--container-2xl);
    margin: 0 auto;
    padding-left: var(--space-md);
    padding-right: var(--space-md);
}

@media (min-width: 640px) {
    .container {
        padding-left: var(--space-lg);
        padding-right: var(--space-lg);
    }
}

@media (min-width: 1024px) {
    .container {
        padding-left: var(--space-xl);
        padding-right: var(--space-xl);
    }
}

@media (min-width: 1400px) {
    .container {
        padding-left: var(--space-3xl);
        padding-right: var(--space-3xl);
    }
}

/* ===== PROFESSIONAL TYPOGRAPHY SYSTEM ===== */
/* Import professional sans-serif fonts used by top IT companies */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Base typography - 85% of original sizes for better readability */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px; /* Reduced from 16px */
    line-height: 1.6;
    font-weight: 400;
    color: var(--color-gray-700);
}

/* Professional heading hierarchy */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--space-md);
    color: var(--color-gray-900);
    letter-spacing: -0.015em;
}

/* Hero and main headings - reduced by 15% */
h1 {
    font-size: clamp(1.7rem, 3.4vw, 3.4rem); /* Was 2rem-4rem */
    font-weight: 700;
    letter-spacing: -0.025em;
    line-height: 1.2;
}

/* Section headings - reduced by 15% */
h2 {
    font-size: clamp(1.275rem, 2.55vw, 2.55rem); /* Was 1.5rem-3rem */
    font-weight: 600;
    letter-spacing: -0.02em;
    line-height: 1.25;
}

/* Subsection headings - reduced by 15% */
h3 {
    font-size: clamp(0.956rem, 1.7vw, 1.488rem); /* Was 1.125rem-1.75rem */
    font-weight: 600;
    letter-spacing: -0.015em;
    line-height: 1.3;
}

/* Card and component headings - reduced by 15% */
h4 {
    font-size: clamp(0.85rem, 1.275vw, 1.169rem); /* Was 1rem-1.375rem */
    font-weight: 600;
    letter-spacing: -0.01em;
    line-height: 1.35;
}

/* Small headings - reduced by 15% */
h5, h6 {
    font-size: clamp(0.765rem, 1.02vw, 1.02rem); /* Was 0.9rem-1.2rem */
    font-weight: 500;
    letter-spacing: -0.005em;
    line-height: 1.4;
}

/* Body text - professional sizing */
p {
    font-family: 'Inter', sans-serif;
    margin-bottom: var(--space-md);
    max-width: var(--content-wide);
    color: var(--color-gray-600);
    font-size: clamp(0.744rem, 0.85vw, 0.85rem); /* Was 0.875rem-1rem */
    line-height: 1.65;
    font-weight: 400;
}

/* Large body text for hero sections */
.text-lg {
    font-size: clamp(0.935rem, 1.105vw, 1.105rem); /* Was 1.1rem-1.3rem */
    line-height: 1.6;
}

/* Small text for captions and metadata */
.text-sm {
    font-size: clamp(0.663rem, 0.765vw, 0.765rem); /* Was 0.78rem-0.9rem */
    line-height: 1.5;
}

/* Extra small text for fine print */
.text-xs {
    font-size: clamp(0.595rem, 0.68vw, 0.68rem); /* Was 0.7rem-0.8rem */
    line-height: 1.4;
}

/* Professional button text */
.btn {
    font-size: 0.744rem; /* Was 0.875rem */
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* Navigation text */
.nav-link {
    font-size: 0.744rem; /* Was 0.875rem */
    font-weight: 500;
    letter-spacing: 0.005em;
}

/* Ensure all elements use professional font stack */
body, input, textarea, select, button, .btn, .nav-link {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
}

/* Professional typography utility classes used by top IT companies */
.text-display {
    font-size: clamp(2.125rem, 4.25vw, 4.25rem);
    font-weight: 700;
    letter-spacing: -0.03em;
    line-height: 1.1;
}

.text-headline {
    font-size: clamp(1.7rem, 3.4vw, 3.4rem);
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1.2;
}

.text-title {
    font-size: clamp(1.275rem, 2.55vw, 2.55rem);
    font-weight: 600;
    letter-spacing: -0.02em;
    line-height: 1.25;
}

.text-subtitle {
    font-size: clamp(0.956rem, 1.7vw, 1.488rem);
    font-weight: 600;
    letter-spacing: -0.015em;
    line-height: 1.3;
}

.text-body {
    font-size: clamp(0.744rem, 0.85vw, 0.85rem);
    font-weight: 400;
    line-height: 1.65;
}

.text-caption {
    font-size: clamp(0.663rem, 0.765vw, 0.765rem);
    font-weight: 400;
    line-height: 1.5;
}

.text-overline {
    font-size: clamp(0.595rem, 0.68vw, 0.68rem);
    font-weight: 500;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    line-height: 1.4;
}

/* Font weight utilities */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

/* Letter spacing utilities */
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0; }
.tracking-wide { letter-spacing: 0.025em; }

.text-center p {
    margin-left: auto;
    margin-right: auto;
}

/* ===== STEP 3 & 5: SECTIONAL CONTAINMENT ===== */
.section {
    padding: var(--space-4xl) 0;
    position: relative;
}

.section-contained {
    background: var(--color-white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    margin: var(--space-2xl) 0;
    padding: var(--space-3xl);
    border: 1px solid var(--color-gray-100);
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-4xl);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-header h2 {
    margin-bottom: var(--space-lg);
}

.section-header p {
    font-size: 1.25rem;
    color: var(--color-gray-600);
}

/* ===== STEP 8: GRID SYSTEM ===== */
.grid {
    display: grid;
    gap: var(--space-xl);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* ===== STEP 3 & 7: MODERN CARDS ===== */
.card {
    background: var(--color-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-gray-100);
    padding: var(--space-2xl);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--color-gray-200);
}

.card:hover::before {
    transform: scaleX(1);
}

/* ===== TECHNOLOGY CAROUSEL ANIMATION ===== */
@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

.tech-carousel {
    animation: scroll 30s linear infinite;
    width: max-content;
}

.tech-carousel:hover {
    animation-play-state: paused;
}

.tech-item {
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.tech-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* ===== STEP 11: INTERACTIVE ELEMENTS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid transparent;
    cursor: pointer;
    min-height: 44px; /* Accessibility: minimum touch target */
    position: relative;
    overflow: hidden;
    letter-spacing: -0.01em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    text-decoration: none;
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    color: var(--color-white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
}

.btn-secondary {
    background: var(--color-white);
    color: var(--color-primary);
    border-color: var(--color-gray-200);
    box-shadow: var(--shadow-sm);
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.btn-secondary:hover {
    background: var(--color-gray-50);
    border-color: var(--color-primary);
    box-shadow: var(--shadow-md);
}

/* ===== NAVIGATION SYSTEM ===== */
.header-gradient {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-link {
    color: var(--color-white);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-height: 40px; /* Touch target */
    display: flex;
    align-items: center;
    letter-spacing: -0.01em;
}

.nav-link:hover {
    color: var(--color-white);
    text-decoration: none;
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.nav-link.active {
    color: var(--color-white);
    font-weight: 700;
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -2px;
    width: 6px;
    height: 6px;
    background: var(--color-white);
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }

.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.z-10 { z-index: 10; }
.z-50 { z-index: 50; }

/* ===== STEP 9: BREATHING ROOM ===== */
.space-y-sm > * + * { margin-top: var(--space-sm); }
.space-y-md > * + * { margin-top: var(--space-md); }
.space-y-lg > * + * { margin-top: var(--space-lg); }
.space-y-xl > * + * { margin-top: var(--space-xl); }

.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }
.mb-2xl { margin-bottom: var(--space-2xl); }

.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }
.mt-2xl { margin-top: var(--space-2xl); }

/* ===== STEP 10: ANIMATIONS & VISUAL RHYTHM ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* ===== FOOTER SYSTEM ===== */
.footer-bg {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    position: relative;
}

.footer-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* ===== SCROLL TO TOP ===== */
#scroll-to-top {
    position: fixed;
    bottom: var(--space-xl);
    right: var(--space-xl);
    background: var(--color-primary);
    color: var(--color-white);
    border: none;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    display: none;
}

#scroll-to-top:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    background: var(--color-primary-dark);
}

/* ===== STEP 6 & 12: RESPONSIVE GUTTER STRATEGY ===== */
@media (max-width: 480px) {
    .container {
        padding-left: var(--space-sm);
        padding-right: var(--space-sm);
    }

    .section {
        padding: var(--space-2xl) 0;
    }

    .section-contained {
        padding: var(--space-xl);
        margin: var(--space-md) 0;
        border-radius: var(--radius-lg);
    }

    .card {
        padding: var(--space-lg);
    }

    .btn {
        padding: var(--space-md) var(--space-lg);
        font-size: 0.875rem;
    }

    h1 { font-size: 1.7rem; } /* Reduced from 2rem (85%) */
    h2 { font-size: 1.488rem; } /* Reduced from 1.75rem (85%) */
    h3 { font-size: 1.275rem; } /* Reduced from 1.5rem (85%) */

    p {
        font-size: 0.85rem; /* Reduced from 1rem (85%) */
        line-height: 1.65;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    .section {
        padding: var(--space-3xl) 0;
    }

    .grid {
        gap: var(--space-lg);
    }
}

@media (min-width: 1600px) {
    .container {
        max-width: var(--container-max);
    }

    .section {
        padding: var(--space-5xl) 0;
    }

    .section-contained {
        padding: var(--space-4xl);
    }
}

/* ===== SPECIAL COMPONENTS ===== */
.hero-section {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.4;
}

/* ===== UNIFIED HEADER-HERO GRADIENT ===== */
.header-hero-gradient {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
    backdrop-filter: blur(10px);
    border-bottom: none;
}

/* ===== NEW HERO SECTION WITH CLEAN GRADIENT ===== */
.hero-section-new {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
    position: relative;
    overflow: hidden;
    height: 75vh; /* Fixed height to 65% of viewport */
    display: flex;
    align-items: center;
    padding: 0;
    margin-top: -5rem; /* Overlap with header for seamless blend */
    padding-top: 5rem; /* Account for header height */
}

/* Remove background patterns for clean look */
.hero-section-new::before {
    display: none;
}

.hero-section-new .container {
    position: relative;
    z-index: 10;
}

/* Floating animation for decorative elements */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-10px) rotate(5deg);
    }
}

.hero-section-new .absolute {
    animation: float 6s ease-in-out infinite;
}

.hero-section-new .absolute:nth-child(2) {
    animation-delay: -2s;
}

.hero-section-new .absolute:nth-child(3) {
    animation-delay: -4s;
}

.hero-section-new .absolute:nth-child(4) {
    animation-delay: -1s;
}

.hero-section-new .absolute:nth-child(5) {
    animation-delay: -3s;
}

/* ===== AI CHIP STYLES ===== */
.ai-chip-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.9;
    transform: scale(1.1); /* Make it slightly bigger */
}

.ai-chip-svg {
    width: 100%;
    max-width: 600px;
    height: auto;
    filter: drop-shadow(0 0 30px rgba(255,255,255,0.15));
}

/* Neural path animations */
.neural-path {
    stroke-dasharray: 5, 5;
    animation: neural-pulse 3s ease-in-out infinite;
}

.neural-path:nth-child(2) {
    animation-delay: -0.5s;
}

.neural-path:nth-child(3) {
    animation-delay: -1s;
}

.neural-path:nth-child(4) {
    animation-delay: -1.5s;
}

.neural-path:nth-child(5) {
    animation-delay: -2s;
}

.neural-path:nth-child(6) {
    animation-delay: -2.5s;
}

@keyframes neural-pulse {
    0%, 100% {
        opacity: 0.4;
        stroke-width: 2;
    }
    50% {
        opacity: 0.8;
        stroke-width: 3;
    }
}

/* Neural nodes glow animation */
.neural-nodes circle {
    animation: node-glow 2s ease-in-out infinite alternate;
}

.neural-nodes circle:nth-child(2) {
    animation-delay: -0.3s;
}

.neural-nodes circle:nth-child(3) {
    animation-delay: -0.6s;
}

.neural-nodes circle:nth-child(4) {
    animation-delay: -0.9s;
}

@keyframes node-glow {
    0% {
        opacity: 0.6;
    }
    100% {
        opacity: 1;
        filter: drop-shadow(0 0 8px rgba(255,255,255,0.8));
    }
}

/* Responsive AI chip sizing */
@media (max-width: 768px) {
    .ai-chip-container {
        transform: scale(0.9);
    }
    .ai-chip-svg {
        max-width: 450px;
    }
}

@media (max-width: 480px) {
    .ai-chip-container {
        transform: scale(0.8);
    }
    .ai-chip-svg {
        max-width: 350px;
    }
}

/* ===== DYNAMIC INTERACTIVE FEATURE CARDS ===== */
.feature-card-interactive {
    position: relative;
    background: white;
    border-radius: 1rem;
    padding: 2rem 1.5rem;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.feature-card-interactive:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 10px 20px -5px rgba(0, 0, 0, 0.1);
}

.feature-icon-container {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem auto;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.feature-card-interactive:hover .feature-icon-container {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.feature-card-title {
    font-family: 'Inter', sans-serif;
    font-size: 0.956rem; /* Reduced from 1.125rem (85%) */
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
    letter-spacing: -0.015em;
    line-height: 1.3;
}

.feature-card-description {
    font-family: 'Inter', sans-serif;
    font-size: 0.744rem; /* Reduced from 0.875rem (85%) */
    color: #6b7280;
    line-height: 1.65;
    font-weight: 400;
}

.feature-card-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    border-radius: 1rem;
    transition: opacity 0.3s ease;
    pointer-events: none;
    filter: blur(20px);
    transform: scale(0.8);
}

.feature-card-interactive:hover .feature-card-glow {
    opacity: 0.1;
}

/* Individual card hover effects */
.feature-card-interactive:hover .bg-indigo-500 {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.feature-card-interactive:hover .bg-purple-500 {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.feature-card-interactive:hover .bg-teal-500 {
    background: linear-gradient(135deg, #14b8a6, #06b6d4);
}

.feature-card-interactive:hover .bg-orange-500 {
    background: linear-gradient(135deg, #f97316, #fb923c);
}

/* Responsive adjustments for feature cards */
@media (max-width: 768px) {
    .feature-card-interactive {
        padding: 1.5rem 1rem;
    }

    .feature-icon-container {
        width: 3.5rem;
        height: 3.5rem;
        margin-bottom: 1rem;
    }

    .feature-card-title {
        font-size: 0.85rem; /* Reduced from 1rem (85%) */
    }

    .feature-card-description {
        font-size: 0.68rem; /* Reduced from 0.8rem (85%) */
    }
}

@media (max-width: 480px) {
    .feature-card-interactive {
        padding: 1.25rem 0.75rem;
    }

    .feature-card-interactive:hover {
        transform: translateY(-4px) scale(1.01);
    }
}

.feature-card {
    /* Alias for .card with specific styling */
    background: var(--color-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-gray-100);
    padding: var(--space-2xl);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--color-gray-200);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

/* ===== ACCESSIBILITY & FOCUS STATES ===== */
*:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

.btn:focus,
.nav-link:focus {
    outline: 2px solid var(--color-white);
    outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
    .header-gradient,
    .footer-bg,
    #scroll-to-top,
    .btn {
        display: none !important;
    }

    .section {
        padding: var(--space-md) 0;
        break-inside: avoid;
    }

    .card {
        border: 1px solid var(--color-gray-300);
        box-shadow: none;
        break-inside: avoid;
    }
}

/* ===== IMPROVED GUTTER SYSTEM & REDUCED WHITE SPACE ===== */
/* Ensure content never touches browser edges while reducing excessive spacing */

/* Override hero section to reduce height */
.hero-section {
    min-height: 85vh !important;
    padding-top: 6rem !important;
    padding-bottom: 3rem !important;
}

.hero-section-new {
    height: 75vh !important;
    min-height: auto !important;
    padding: 0 !important;
    margin-top: -5rem !important;
    padding-top: 5rem !important;
}

/* Reduce section padding for tighter layout */
.section {
    padding: var(--space-2xl) 0 !important;
}

/* Specific section padding overrides */
section {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
}

/* Compact grid spacing */
.grid {
    gap: var(--space-lg) !important;
}

@media (min-width: 768px) {
    .grid {
        gap: var(--space-xl) !important;
    }
}

/* Ensure proper horizontal gutters */
body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Prevent content from touching edges */
section {
    padding-left: max(var(--space-md), env(safe-area-inset-left)) !important;
    padding-right: max(var(--space-md), env(safe-area-inset-right)) !important;
}

@media (min-width: 640px) {
    section {
        padding-left: max(var(--space-lg), env(safe-area-inset-left)) !important;
        padding-right: max(var(--space-lg), env(safe-area-inset-right)) !important;
    }
}

/* Compact mobile spacing */
@media (max-width: 640px) {
    .hero-section {
        min-height: 70vh !important;
        padding-top: 5rem !important;
        padding-bottom: 2rem !important;
    }

    .section {
        padding: var(--space-2xl) 0 !important;
    }

    .grid {
        gap: var(--space-md) !important;
    }
}

/* Reduce footer spacing */
.footer-bg {
    padding: var(--space-2xl) 0 !important;
}

/* Tighter section headers */
.section-header {
    margin-bottom: var(--space-2xl) !important;
}

@media (min-width: 768px) {
    .section-header {
        margin-bottom: var(--space-3xl) !important;
    }
}
