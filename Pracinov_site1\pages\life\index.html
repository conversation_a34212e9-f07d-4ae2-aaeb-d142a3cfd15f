<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Life@ Pracinov</title>
    <meta name="description" content="Discover what it's like to work at Pracinov. Learn about our culture, values, and team.">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/assets/css/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
</head>
<body>
    <!-- Header -->
    <header class="header-gradient backdrop-blur-md sticky top-0 z-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <div class="flex items-center">
                    <span class="material-icons text-white text-4xl">integration_instructions</span>
                    <span class="ml-3 text-3xl font-bold text-white">Pracinov</span>
                </div>
                <nav class="hidden md:flex space-x-6 items-center">
                    <a class="nav-link text-white" href="/pages/home/">Home</a>
                    <a class="nav-link text-white" href="/pages/about/">About Us</a>
                    <a class="nav-link text-white" href="/pages/services/">Services</a>
                    <a class="nav-link active text-white" href="/pages/life/">Life@ Pracinov</a>
                    <a class="nav-link text-white" href="/pages/careers/">Careers</a>
                </nav>
                <button class="hidden md:block bg-white/90 hover:bg-white text-indigo-700 font-semibold py-3 px-6 rounded-lg text-sm shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:scale-105">
                    Book Consultation
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="hero-life">
            <div class="container relative z-10">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[500px]">
                    <!-- Left Content -->
                    <div class="text-left">
                        <h1 class="text-2xl lg:text-4xl font-bold text-white mb-6 leading-tight">
                            Life@ <span class="text-yellow-300">Pracinov</span>
                        </h1>
                        <p class="text-sm lg:text-base text-white/90 mb-8 leading-relaxed">
                            Discover our vibrant culture, collaborative environment, and the amazing people who make Pracinov a great place to work and grow.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <button class="bg-white text-indigo-700 font-medium py-3 px-6 rounded-lg text-sm shadow-lg hover:shadow-xl transition-all duration-200 ease-in-out hover:bg-gray-50">
                                Join Our Team
                            </button>
                            <button class="border border-white text-white font-medium py-3 px-6 rounded-lg text-sm hover:bg-white hover:text-indigo-700 transition-all duration-200 ease-in-out">
                                Meet The Team
                            </button>
                        </div>
                    </div>

                    <!-- Right Image -->
                    <div class="relative lg:block hidden">
                        <div class="hero-image-container">
                            <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                                 alt="Team collaboration and culture"
                                 class="hero-image-blended">
                            <div class="hero-image-overlay"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Culture Values Section -->
        <section class="bg-gray-50 py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Our Vibrant Culture</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">At Pracinov, we believe that great technology comes from great people. Our culture is built on collaboration, innovation, and mutual respect.</p>
                </div>

                <!-- Culture Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    <!-- Innovation First -->
                    <div class="group bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-purple-100 hover:border-purple-200 transform hover:-translate-y-2 cursor-pointer relative overflow-hidden">
                        <div class="absolute inset-0 opacity-5">
                            <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                                <pattern id="innovation-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                                    <circle cx="2" cy="2" r="1" fill="currentColor"/>
                                    <circle cx="12" cy="12" r="1" fill="currentColor"/>
                                </pattern>
                                <rect width="100" height="100" fill="url(#innovation-pattern)"/>
                            </svg>
                        </div>

                        <div class="relative z-10">
                            <div class="flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-purple-500 to-indigo-600 text-white mb-6 mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-4 text-center group-hover:text-purple-700 transition-colors">Innovation First</h3>
                            <p class="text-gray-600 text-sm text-center leading-relaxed">We encourage creative thinking and provide the freedom to explore new ideas and technologies.</p>
                        </div>
                    </div>

                    <!-- Collaborative Spirit -->
                    <div class="group bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-blue-100 hover:border-blue-200 transform hover:-translate-y-2 cursor-pointer relative overflow-hidden">
                        <div class="absolute inset-0 opacity-5">
                            <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                                <pattern id="collab-pattern" x="0" y="0" width="15" height="15" patternUnits="userSpaceOnUse">
                                    <rect x="2" y="2" width="2" height="2" fill="currentColor"/>
                                    <rect x="8" y="8" width="2" height="2" fill="currentColor"/>
                                </pattern>
                                <rect width="100" height="100" fill="url(#collab-pattern)"/>
                            </svg>
                        </div>

                        <div class="relative z-10">
                            <div class="flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-600 text-white mb-6 mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-4 text-center group-hover:text-blue-700 transition-colors">Collaborative Spirit</h3>
                            <p class="text-gray-600 text-sm text-center leading-relaxed">We believe in the power of teamwork and open communication across all levels of the organization.</p>
                        </div>
                    </div>

                    <!-- Growth Mindset -->
                    <div class="group bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-emerald-100 hover:border-emerald-200 transform hover:-translate-y-2 cursor-pointer relative overflow-hidden">
                        <div class="absolute inset-0 opacity-5">
                            <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                                <pattern id="growth-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse">
                                    <circle cx="5" cy="5" r="2" fill="currentColor"/>
                                    <circle cx="15" cy="15" r="1" fill="currentColor"/>
                                </pattern>
                                <rect width="100" height="100" fill="url(#growth-pattern)"/>
                            </svg>
                        </div>

                        <div class="relative z-10">
                            <div class="flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-emerald-500 to-teal-600 text-white mb-6 mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-4 text-center group-hover:text-emerald-700 transition-colors">Growth Mindset</h3>
                            <p class="text-gray-600 text-sm text-center leading-relaxed">We invest in our people's development and provide opportunities for continuous learning and career advancement.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Team Spotlight Section -->
        <section class="bg-white py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Meet Our Amazing People</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">The heart of Pracinov is our incredible team of passionate professionals who bring their unique talents and perspectives to everything we do.</p>
                </div>

                <!-- Team Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    <!-- Team Member 1 -->
                    <div class="group bg-gradient-to-br from-purple-50 to-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                        <div class="relative mb-6">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80"
                                 alt="Team Member"
                                 class="w-24 h-24 rounded-full mx-auto object-cover border-4 border-purple-200 group-hover:border-purple-400 transition-colors">
                            <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">🚀</span>
                            </div>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2 text-center">Alex Chen</h3>
                        <p class="text-purple-600 font-medium mb-3 text-center">Senior Full-Stack Developer</p>
                        <p class="text-gray-600 text-sm text-center leading-relaxed mb-4">
                            "I love how we're encouraged to experiment with new technologies and share our learnings with the team."
                        </p>
                        <div class="flex justify-center space-x-2">
                            <span class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">React</span>
                            <span class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">Node.js</span>
                        </div>
                    </div>

                    <!-- Team Member 2 -->
                    <div class="group bg-gradient-to-br from-blue-50 to-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                        <div class="relative mb-6">
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80"
                                 alt="Team Member"
                                 class="w-24 h-24 rounded-full mx-auto object-cover border-4 border-blue-200 group-hover:border-blue-400 transition-colors">
                            <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">🎨</span>
                            </div>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2 text-center">Sarah Johnson</h3>
                        <p class="text-blue-600 font-medium mb-3 text-center">UX/UI Designer</p>
                        <p class="text-gray-600 text-sm text-center leading-relaxed mb-4">
                            "The collaborative environment here allows me to create designs that truly make a difference for our users."
                        </p>
                        <div class="flex justify-center space-x-2">
                            <span class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">Figma</span>
                            <span class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">Design Systems</span>
                        </div>
                    </div>

                    <!-- Team Member 3 -->
                    <div class="group bg-gradient-to-br from-emerald-50 to-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                        <div class="relative mb-6">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
                                 alt="Team Member"
                                 class="w-24 h-24 rounded-full mx-auto object-cover border-4 border-emerald-200 group-hover:border-emerald-400 transition-colors">
                            <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">☁️</span>
                            </div>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2 text-center">Michael Rodriguez</h3>
                        <p class="text-emerald-600 font-medium mb-3 text-center">DevOps Engineer</p>
                        <p class="text-gray-600 text-sm text-center leading-relaxed mb-4">
                            "Working at Pracinov means being part of a team that values innovation and continuous improvement."
                        </p>
                        <div class="flex justify-center space-x-2">
                            <span class="px-3 py-1 bg-emerald-100 text-emerald-700 rounded-full text-xs">AWS</span>
                            <span class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">Docker</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Work Environment Section -->
        <section class="bg-gradient-to-br from-purple-50 via-indigo-50 to-blue-50 py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Our Work Environment</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">We've created a workspace that fosters creativity, collaboration, and well-being.</p>
                </div>

                <!-- Environment Features -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                    <div class="text-center bg-white/60 rounded-xl p-6 backdrop-blur-sm border border-white/50 hover:bg-white/80 transition-all duration-300 hover:transform hover:-translate-y-1">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Modern Office</h3>
                        <p class="text-sm text-gray-600">State-of-the-art facilities designed for productivity and comfort</p>
                    </div>

                    <div class="text-center bg-white/60 rounded-xl p-6 backdrop-blur-sm border border-white/50 hover:bg-white/80 transition-all duration-300 hover:transform hover:-translate-y-1">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                            </svg>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Flexible Work</h3>
                        <p class="text-sm text-gray-600">Hybrid work options that support work-life balance</p>
                    </div>

                    <div class="text-center bg-white/60 rounded-xl p-6 backdrop-blur-sm border border-white/50 hover:bg-white/80 transition-all duration-300 hover:transform hover:-translate-y-1">
                        <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Learning Hub</h3>
                        <p class="text-sm text-gray-600">Continuous learning opportunities and skill development</p>
                    </div>

                    <div class="text-center bg-white/60 rounded-xl p-6 backdrop-blur-sm border border-white/50 hover:bg-white/80 transition-all duration-300 hover:transform hover:-translate-y-1">
                        <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Wellness Focus</h3>
                        <p class="text-sm text-gray-600">Health and wellness programs for our team's well-being</p>
                    </div>
                </div>

                <!-- Call to Action -->
                <div class="text-center bg-white/80 rounded-2xl p-8 backdrop-blur-sm border border-white/50">
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Ready to Join Our Team?</h3>
                    <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
                        We're always looking for talented individuals who share our passion for innovation and excellence.
                        Explore our current openings and become part of the Pracinov family.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button class="bg-purple-600 text-white font-medium py-4 px-8 rounded-lg text-base shadow-lg hover:shadow-xl transition-all duration-200 ease-in-out hover:bg-purple-700">
                            View Open Positions
                        </button>
                        <button class="border-2 border-purple-600 text-purple-600 font-medium py-4 px-8 rounded-lg text-base hover:bg-purple-600 hover:text-white transition-all duration-200 ease-in-out">
                            Learn About Benefits
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer-bg py-16">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                <div>
                    <div class="flex items-center mb-6">
                        <span class="material-icons text-white text-3xl mr-3">integration_instructions</span>
                        <h3 class="text-2xl font-bold text-white">Pracinov</h3>
                    </div>
                    <p class="text-white/80 leading-relaxed mb-6">Premium IT solutions for forward-thinking businesses.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/assets/js/main.js"></script>
</body>
</html>
